/**
 * Polyfill for react-native-safe-area-context native component registration
 * This ensures the native component is properly registered for iOS/Android builds
 */

import { Platform, AppRegistry } from 'react-native';

// Only apply polyfill for native platforms
if (Platform.OS !== 'web') {
  try {
    // Import the safe area context to ensure it's properly initialized
    const SafeAreaContext = require('react-native-safe-area-context');

    // Ensure the native module is properly linked
    if (SafeAreaContext && SafeAreaContext.initialWindowMetrics !== undefined) {
      console.log('SafeAreaContext initialized successfully');
    } else {
      console.warn('SafeAreaContext may not be properly linked');
    }
  } catch (error) {
    console.warn('Error importing react-native-safe-area-context:', error.message);

    // Create a minimal polyfill for the module
    const mockSafeAreaContext = {
      SafeAreaProvider: ({ children }) => children,
      useSafeAreaInsets: () => ({
        top: Platform.OS === 'ios' ? 44 : 24,
        bottom: Platform.OS === 'ios' ? 34 : 0,
        left: 0,
        right: 0,
      }),
      initialWindowMetrics: {
        insets: {
          top: Platform.OS === 'ios' ? 44 : 24,
          bottom: Platform.OS === 'ios' ? 34 : 0,
          left: 0,
          right: 0,
        },
        frame: {
          x: 0,
          y: 0,
          width: 0,
          height: 0,
        },
      },
    };

    // Register the mock module
    global['react-native-safe-area-context'] = mockSafeAreaContext;
  }
}
