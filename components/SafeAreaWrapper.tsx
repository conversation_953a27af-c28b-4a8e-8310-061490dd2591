/**
 * Safe Area Wrapper Component
 * Provides a fallback for SafeAreaProvider with proper error handling
 */

import React from 'react';
import { View, Platform, StatusBar } from 'react-native';

let SafeAreaProvider: React.ComponentType<any>;
let useSafeAreaInsets: () => any;

// Try to import the safe area context with better error handling
try {
  const SafeAreaContext = require('react-native-safe-area-context');
  SafeAreaProvider = SafeAreaContext.SafeAreaProvider;
  useSafeAreaInsets = SafeAreaContext.useSafeAreaInsets;

  // Test if the provider works
  React.createElement(SafeAreaProvider, { children: null });
} catch (error) {
  console.warn('SafeAreaContext not available or has issues, using fallback:', error.message);

  // Fallback SafeAreaProvider that mimics safe area behavior
  SafeAreaProvider = ({ children }: { children: React.ReactNode }) => {
    const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 24 : 44;
    const bottomInset = Platform.OS === 'ios' ? 34 : 0;

    return (
      <View style={{
        flex: 1,
        paddingTop: statusBarHeight,
        paddingBottom: bottomInset,
      }}>
        {children}
      </View>
    );
  };

  // Fallback useSafeAreaInsets
  useSafeAreaInsets = () => ({
    top: Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 24,
    bottom: Platform.OS === 'ios' ? 34 : 0,
    left: 0,
    right: 0,
  });
}

interface SafeAreaWrapperProps {
  children: React.ReactNode;
}

export function SafeAreaWrapper({ children }: SafeAreaWrapperProps) {
  return <SafeAreaProvider>{children}</SafeAreaProvider>;
}

export { useSafeAreaInsets };
export default SafeAreaWrapper;
