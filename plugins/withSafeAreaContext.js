/**
 * Custom Expo config plugin for react-native-safe-area-context
 * This ensures proper native module configuration for iOS and Android
 */

const { withDangerousMod, withPlugins } = require('@expo/config-plugins');
const fs = require('fs');
const path = require('path');

function withSafeAreaContext(config) {
  return withPlugins(config, [
    // iOS configuration
    (config) => withDangerousMod(config, [
      'ios',
      async (config) => {
        const projectRoot = config.modRequest.projectRoot;
        const podfilePath = path.join(projectRoot, 'ios', 'Podfile');
        
        if (fs.existsSync(podfilePath)) {
          let podfileContent = fs.readFileSync(podfilePath, 'utf8');
          
          // Ensure RNCSafeAreaView is properly configured
          if (!podfileContent.includes('RNCSafeAreaView')) {
            const podInsert = `
  # react-native-safe-area-context
  pod 'RNCSafeAreaView', :path => '../node_modules/react-native-safe-area-context'
`;
            
            // Insert before the end of the target
            podfileContent = podfileContent.replace(
              /(\s+end\s*$)/m,
              podInsert + '$1'
            );
            
            fs.writeFileSync(podfilePath, podfileContent);
          }
        }
        
        return config;
      },
    ]),
    
    // Android configuration
    (config) => withDangerousMod(config, [
      'android',
      async (config) => {
        const projectRoot = config.modRequest.projectRoot;
        const mainApplicationPath = path.join(
          projectRoot,
          'android',
          'app',
          'src',
          'main',
          'java',
          'com',
          'isotopeai',
          'focus',
          'MainApplication.java'
        );
        
        if (fs.existsSync(mainApplicationPath)) {
          let content = fs.readFileSync(mainApplicationPath, 'utf8');
          
          // Add import for SafeAreaContext
          if (!content.includes('import com.th3rdwave.safeareacontext.SafeAreaContextPackage;')) {
            content = content.replace(
              /(import.*?;)\n/g,
              '$1\nimport com.th3rdwave.safeareacontext.SafeAreaContextPackage;\n'
            );
          }
          
          // Add package to the list
          if (!content.includes('new SafeAreaContextPackage()')) {
            content = content.replace(
              /(new MainReactPackage\(\))/,
              '$1,\n            new SafeAreaContextPackage()'
            );
          }
          
          fs.writeFileSync(mainApplicationPath, content);
        }
        
        return config;
      },
    ]),
  ]);
}

module.exports = withSafeAreaContext;
